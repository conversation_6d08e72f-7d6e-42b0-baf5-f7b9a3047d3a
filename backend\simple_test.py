#!/usr/bin/env python3
"""
简单的API测试
"""
import urllib.request
import urllib.parse
import json

def test_login_api():
    """测试登录API"""
    url = "http://localhost:8000/api/v1/auth/login"
    
    # 准备form data
    data = urllib.parse.urlencode({
        'username': 'admin',
        'password': 'admin123'
    }).encode('utf-8')
    
    # 创建请求
    req = urllib.request.Request(
        url, 
        data=data,
        headers={
            'Content-Type': 'application/x-www-form-urlencoded'
        }
    )
    
    try:
        with urllib.request.urlopen(req) as response:
            status_code = response.getcode()
            response_data = response.read().decode('utf-8')
            
            print(f"状态码: {status_code}")
            print(f"响应内容: {response_data}")
            
            if status_code == 200:
                result = json.loads(response_data)
                token = result.get('access_token', '')
                print(f"登录成功! Token前50字符: {token[:50]}...")
                return True
            else:
                print("登录失败")
                return False
                
    except Exception as e:
        print(f"请求失败: {e}")
        return False

if __name__ == "__main__":
    print("测试登录API...")
    test_login_api()
