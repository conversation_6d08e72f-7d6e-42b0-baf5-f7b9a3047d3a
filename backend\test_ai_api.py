#!/usr/bin/env python3
"""
测试AI API连接
"""
import asyncio
import httpx
import json
from app.core.config import settings

async def test_ai_api():
    """测试AI API连接"""
    api_url = f"{settings.AI_API_ENDPOINT}/chat/completions"
    api_key = settings.AI_API_KEY
    
    print(f"测试AI API连接...")
    print(f"API端点: {api_url}")
    print(f"API密钥: {api_key[:10]}...")
    print("=" * 50)
    
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {api_key}'
    }
    
    payload = {
        'model': 'gpt-3.5-turbo',
        'messages': [{'role': 'user', 'content': '你好，请简单介绍一下你自己'}],
        'stream': False,
        'max_tokens': 100
    }
    
    try:
        async with httpx.AsyncClient() as client:
            print("发送请求...")
            response = await client.post(
                api_url,
                headers=headers,
                json=payload,
                timeout=30.0
            )
            
            print(f"响应状态码: {response.status_code}")
            print(f"响应头: {dict(response.headers)}")
            
            if response.status_code == 200:
                data = response.json()
                print("✅ API调用成功!")
                print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
                
                if data.get('choices') and len(data['choices']) > 0:
                    content = data['choices'][0]['message']['content']
                    print(f"AI回复: {content}")
                    return True
            else:
                print("❌ API调用失败")
                print(f"错误响应: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

async def test_ai_api_stream():
    """测试AI API流式连接"""
    api_url = f"{settings.AI_API_ENDPOINT}/chat/completions"
    api_key = settings.AI_API_KEY
    
    print(f"\n测试AI API流式连接...")
    print("=" * 50)
    
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {api_key}'
    }
    
    payload = {
        'model': 'gpt-3.5-turbo',
        'messages': [{'role': 'user', 'content': '请用一句话介绍React'}],
        'stream': True,
        'max_tokens': 50
    }
    
    try:
        async with httpx.AsyncClient() as client:
            print("发送流式请求...")
            async with client.stream(
                'POST',
                api_url,
                headers=headers,
                json=payload,
                timeout=60.0
            ) as response:
                print(f"流式响应状态码: {response.status_code}")
                
                if response.status_code == 200:
                    print("✅ 流式API调用成功!")
                    print("AI流式回复: ", end="")
                    
                    async for line in response.aiter_lines():
                        if line.startswith('data: '):
                            try:
                                data_str = line[6:]  # 移除 'data: ' 前缀
                                if data_str == '[DONE]':
                                    print("\n✅ 流式响应完成")
                                    return True
                                data = json.loads(data_str)
                                if data.get('choices') and len(data['choices']) > 0:
                                    delta = data['choices'][0].get('delta', {})
                                    content = delta.get('content', '')
                                    if content:
                                        print(content, end="", flush=True)
                            except json.JSONDecodeError:
                                continue
                    return True
                else:
                    print("❌ 流式API调用失败")
                    print(f"错误响应: {await response.aread()}")
                    return False
                    
    except Exception as e:
        print(f"❌ 流式请求异常: {e}")
        return False

async def main():
    """主测试函数"""
    print("开始AI API测试...")
    
    # 测试普通API
    success1 = await test_ai_api()
    
    # 测试流式API
    success2 = await test_ai_api_stream()
    
    print("\n" + "=" * 50)
    if success1 and success2:
        print("✅ 所有AI API测试通过!")
    else:
        print("❌ AI API测试失败")
        if not success1:
            print("  - 普通API测试失败")
        if not success2:
            print("  - 流式API测试失败")

if __name__ == "__main__":
    asyncio.run(main())
