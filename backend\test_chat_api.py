#!/usr/bin/env python3
"""
测试聊天API
"""
import asyncio
import httpx
import json

async def test_chat_api():
    """测试聊天API"""
    # 首先登录获取token
    login_url = "http://localhost:8000/api/v1/auth/login"
    login_data = {
        'username': 'admin',
        'password': 'admin123'
    }
    
    print("1. 登录获取token...")
    async with httpx.AsyncClient() as client:
        response = await client.post(login_url, data=login_data)
        if response.status_code != 200:
            print(f"❌ 登录失败: {response.text}")
            return False
        
        token_data = response.json()
        token = token_data['access_token']
        print(f"✅ 登录成功，token: {token[:20]}...")
    
    # 创建聊天会话
    chat_url = "http://localhost:8000/api/v1/chat/"
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    chat_create_data = {
        'title': '测试聊天',
        'description': '测试AI API修复'
    }
    
    print("\n2. 创建聊天会话...")
    async with httpx.AsyncClient() as client:
        response = await client.post(chat_url, headers=headers, json=chat_create_data)
        if response.status_code != 200:
            print(f"❌ 创建聊天失败: {response.text}")
            return False
        
        chat_data = response.json()
        chat_id = chat_data['id']
        print(f"✅ 创建聊天成功，chat_id: {chat_id}")
    
    # 发送消息（非流式）
    message_url = "http://localhost:8000/api/v1/chat/message"
    message_data = {
        'message': '你好，请简单介绍一下React',
        'chat_id': chat_id,
        'use_documents': False
    }
    
    print("\n3. 发送消息（非流式）...")
    async with httpx.AsyncClient() as client:
        response = await client.post(message_url, headers=headers, json=message_data, timeout=60.0)
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 消息发送成功!")
            print(f"AI回复: {result['message']}")
            return True
        else:
            print(f"❌ 消息发送失败: {response.text}")
            return False

async def test_chat_stream_api():
    """测试流式聊天API"""
    # 首先登录获取token
    login_url = "http://localhost:8000/api/v1/auth/login"
    login_data = {
        'username': 'admin',
        'password': 'admin123'
    }
    
    print("\n4. 测试流式聊天API...")
    async with httpx.AsyncClient() as client:
        response = await client.post(login_url, data=login_data)
        if response.status_code != 200:
            print(f"❌ 登录失败: {response.text}")
            return False
        
        token_data = response.json()
        token = token_data['access_token']
    
    # 发送流式消息
    stream_url = "http://localhost:8000/api/v1/chat/message/stream"
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    message_data = {
        'message': '请用一句话解释什么是Python',
        'chat_id': 1,  # 使用已存在的chat_id
        'use_documents': False
    }
    
    print("发送流式消息...")
    async with httpx.AsyncClient() as client:
        async with client.stream(
            'POST',
            stream_url,
            headers=headers,
            json=message_data,
            timeout=60.0
        ) as response:
            print(f"流式响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                print("✅ 流式消息发送成功!")
                print("AI流式回复: ", end="")
                
                async for line in response.aiter_lines():
                    if line.startswith('data: '):
                        try:
                            data_str = line[6:]  # 移除 'data: ' 前缀
                            data = json.loads(data_str)
                            if data.get('content'):
                                print(data['content'], end="", flush=True)
                            elif data.get('done'):
                                print("\n✅ 流式响应完成")
                                return True
                        except json.JSONDecodeError:
                            continue
                return True
            else:
                print(f"❌ 流式消息发送失败: {response.text}")
                return False

async def main():
    """主测试函数"""
    print("开始聊天API测试...")
    print("=" * 50)
    
    # 测试普通聊天API
    success1 = await test_chat_api()
    
    # 测试流式聊天API
    success2 = await test_chat_stream_api()
    
    print("\n" + "=" * 50)
    if success1 and success2:
        print("✅ 所有聊天API测试通过!")
    else:
        print("❌ 聊天API测试失败")

if __name__ == "__main__":
    asyncio.run(main())
