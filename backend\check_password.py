#!/usr/bin/env python3
"""
检查用户密码
"""
from app.core.database import SessionLocal
from app.models.user import User
from app.core.security import verify_password

def check_admin_password():
    """检查admin用户密码"""
    db = SessionLocal()
    try:
        admin = db.query(User).filter(User.username == 'admin').first()
        
        if not admin:
            print("Admin用户不存在")
            return
            
        print(f"Admin用户存在: {admin.username}")
        print(f"邮箱: {admin.email}")
        print(f"活跃状态: {admin.is_active}")
        print(f"密码哈希: {admin.hashed_password[:50]}...")
        
        # 测试不同密码
        passwords_to_test = ['admin123', 'admin', 'password', '123456']
        
        for pwd in passwords_to_test:
            result = verify_password(pwd, admin.hashed_password)
            print(f"验证密码 '{pwd}': {result}")
            
    finally:
        db.close()

if __name__ == "__main__":
    check_admin_password()
