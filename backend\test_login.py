#!/usr/bin/env python3
"""
测试登录API
"""
import requests
import json

def test_login():
    """测试登录"""
    url = "http://localhost:8000/api/v1/auth/login"
    
    # 使用form data格式
    data = {
        'username': 'admin',
        'password': 'admin123'
    }
    
    try:
        response = requests.post(url, data=data)
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"登录成功! Token: {result.get('access_token', '')[:50]}...")
            return True
        else:
            print(f"登录失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"请求异常: {e}")
        return False

def test_wrong_password():
    """测试错误密码"""
    url = "http://localhost:8000/api/v1/auth/login"
    
    data = {
        'username': 'admin',
        'password': 'wrongpassword'
    }
    
    try:
        response = requests.post(url, data=data)
        print(f"\n错误密码测试 - 状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
    except Exception as e:
        print(f"请求异常: {e}")

if __name__ == "__main__":
    print("测试登录API...")
    print("=" * 50)
    
    # 测试正确登录
    print("1. 测试正确的用户名密码:")
    test_login()
    
    # 测试错误密码
    print("\n2. 测试错误密码:")
    test_wrong_password()
